package cloud.demand.lab.modules.longterm.cos.web;

import cloud.demand.lab.modules.longterm.predict.web.req.query_and_create_task.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.longterm.predict.web.resp.query_and_create_task.QueryCategoryAndTaskListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 负责查询cos中长期预测任务的信息
 */
@JsonrpcController("/cos-longterm-predict")
@Slf4j
public class CosQueryPredictTaskController {

    @RequestMapping
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(@JsonrpcParam QueryCategoryAndTaskListReq req) {
        return null;
    }

}
